<template>
  <div class="pt-16 px-8 px-sm-4 white">
    <!-- Action buttons -->
    <v-card class="mb-4 grey lighten-4 rounded-lg elevation-2">
      <v-card-text class="d-flex justify-end align-center py-2">
        <v-btn
          v-if="!formAccess || formAccess.add !== false"
          color="primary"
          class="font-weight-medium mr-2 rounded-lg elevation-2"
          prepend-icon="add"
          @click="$emit('open-add-form')"
        >
          <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
          Add New
        </v-btn>
        <v-btn
          color="primary"
          variant="outlined"
          class="ml-2 rounded-lg elevation-2"
          prepend-icon="refresh"
          @click="$emit('refetch-list')"
        >
          <v-icon>fas fa-redo-alt</v-icon>
        </v-btn>
      </v-card-text>
    </v-card>
    <div>
      <v-data-table
        id="doc_template_table"
        v-model="selectedRecords"
        :headers="tableHeaders"
        :items="tableItems"
        item-key="generatedDocumentId"
        :items-per-page="50"
        :items-per-page-options="[
          { value: 50, title: '50' },
          { value: 100, title: '100' },
          { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
        ]"
        :page="page"
        :search="searchValue"
        :show-select="false"
        fixed-header
        :height="$store.getters.getTableHeight(290)"
        :sort-by="[{ key: 'employeeName', order: 'asc' }]"
        class="elevation-4 rounded-xl"
        item-class="white"
        @update:page="page = $event"
        @page-count="pageCount = $event"
      >
        <template #no-results>
          <AppFetchErrorScreen
            key="no-results-screen"
            button-text="Clear All"
            icon-name="fas fa-sync"
            main-title="No matching search results found."
            content="Please try again by changing the filters or clear it to get all data."
            image-name="common/no-records"
            @button-click="clearFilter()"
          />
        </template>

        <!-- table item slots -->
        <template #item="{ item }">
          <tr
            :id="'doc_template_list__tr_' + item.generatedDocumentId"
            class="data-table-tr bg-white cursor-pointer"
            :class="
              isMobileView
                ? 'v-data-table__mobile-table-row ma-0 mt-2'
                : ''
            "
            @click="selectDocTemplateRecord(item)"
          >
            <!-- Employee Name -->
            <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
              <div v-if="isMobileView" class="v-data-table__mobile-row__header">
                Employee Name
              </div>
              <div
                :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
              >
                <section class="secondary--text body-2 font-weight-medium">
                  {{ checkNullValue(item.employeeName) }}
                </section>
                <div
                  v-if="!isMobileView"
                  class="grey--text caption text-truncate"
                  style="width: 120px; overflow-wrap: break-word"
                >
                  {{
                    item.userDefinedEmployeeId ? item.userDefinedEmployeeId : ""
                  }}
                </div>
              </div>
            </td>

            <!-- Candidate Name -->
            <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
              <div v-if="isMobileView" class="v-data-table__mobile-row__header">
                Candidate Name
              </div>
              <div
                :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
              >
                <section class="primary--text body-2 font-weight-medium">
                  {{ checkNullValue(item.candidateName) }}
                </section>
                <div
                  v-if="!isMobileView"
                  class="grey--text caption text-truncate"
                  style="width: 120px; overflow-wrap: break-word"
                >
                  {{
                    item.userDefinedCandidateId
                      ? item.userDefinedCandidateId
                      : ""
                  }}
                </div>
              </div>
            </td>

            <!-- Document Name -->
            <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
              <div v-if="isMobileView" class="v-data-table__mobile-row__header">
                Document Name
              </div>
              <div
                :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
              >
                <section class="primary--text body-2">
                  {{ checkNullValue(item.documentName) }}
                </section>
              </div>
            </td>

            <!-- Signatories -->
            <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
              <div v-if="isMobileView" class="v-data-table__mobile-row__header">
                Signatories
              </div>
              <div
                :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
                class="primary--text body-2"
              >
                <section
                  v-if="
                    item.authorizedSignatories &&
                    item.authorizedSignatories.length > 0
                  "
                  class="d-flex align-center"
                >
                  <v-menu :open-on-hover="!isMobileView" right offset-x>
                    <template #activator="{ on, attrs }">
                      <div v-bind="attrs" v-on="on">
                        <v-avatar
                          v-for="(signatory, index) in JSON.parse(
                            item.authorizedSignatories
                          )"
                          :key="index + 'avatar'"
                          size="31"
                          :color="
                            signatory.status === 'Signed'
                              ? '#00c619'
                              : 'grey lighten-1'
                          "
                          class="ml-1 caption white--text font-weight-medium"
                        >
                          {{ letterAvatar(signatory.signatureEmployeeName) }}
                        </v-avatar>
                      </div>
                    </template>

                    <v-list>
                      <v-list-item
                        v-for="(signatory, index) in JSON.parse(
                          item.authorizedSignatories
                        )"
                        :key="index + 'menuList'"
                      >
                        <v-list-item-avatar
                          size="30"
                          class="avatarClass"
                          :color="
                            signatory.status === 'Signed'
                              ? '#00c619'
                              : 'grey lighten-1'
                          "
                          :class="['avatarBCColor' + (index % 5)]"
                        >
                          <span
                            class="mx-auto caption font-weight-medium white--text"
                            >{{
                              letterAvatar(signatory.signatureEmployeeName)
                            }}</span
                          >
                        </v-list-item-avatar>
                        <v-list-item-content style="max-width: 160px">
                          <v-list-item-title
                            style="max-width: 150px"
                            class="text-truncate"
                          >
                            {{ signatory.signatureEmployeeName }}
                          </v-list-item-title>
                          <v-list-item-subtitle
                            :class="
                              signatory.status === 'Signed'
                                ? 'completed-status-text'
                                : 'grey--text'
                            "
                          >
                            {{ signatory.status }}
                          </v-list-item-subtitle>
                        </v-list-item-content>
                        <v-btn
                          v-if="
                            signatory.status === 'Not Signed' &&
                            signatory.emailId &&
                            !['Declined', 'Rejected', 'Draft'].includes(
                              item.status
                            )
                          "
                          color="primary"
                          small
                          class="mr-1"
                          style="max-width: 75px"
                          :disabled="
                            signatory?.signatureKey?.toLowerCase() ===
                              'candidate' &&
                            !checkIfSignedByAll(
                              JSON.parse(item?.authorizedSignatories)
                            )
                          "
                          @click.stop="
                            resendEmailToSignatories(item, signatory)
                          "
                        >
                          {{ signatory?.emailSent ? "Resend " : "Send" }}
                          <v-icon size="13" color="white"> send </v-icon>
                        </v-btn>
                        <v-btn
                          v-else-if="
                            signatory.status === 'Not Signed' &&
                            signatory.emailId &&
                            ['Declined', 'Rejected'].includes(item.status)
                          "
                          v-tooltip="{
                            content:
                              item.status === 'Declined'
                                ? 'Resend not allowed as the candidate declined the offer'
                                : item.status === 'Rejected'
                                ? 'Resend not allowed as the manager rejected the offer'
                                : '',
                            trigger: 'hover',
                            placement: 'bottom',
                          }"
                          color="grey"
                          small
                          class="mr-1"
                          style="
                            max-width: 75px;
                            cursor: not-allowed;
                            opacity: 0.5;
                          "
                        >
                          {{ signatory?.emailSent ? "Resend " : "Send" }}
                          <v-icon size="13" color="white"> send </v-icon>
                        </v-btn>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </section>
                <section v-else>-</section>
              </div>
            </td>

            <!-- Status -->
            <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
              <div v-if="isMobileView" class="v-data-table__mobile-row__header">
                Status
              </div>
              <div
                :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
              >
                <section
                  class="d-flex align-center px-3 py-1 rounded-pill caption font-weight-bold"
                  :class="
                    item.status === 'Completed'
                      ? 'success--text green lighten-5'
                      : item.status === 'In Review'
                      ? 'warning--text yellow lighten-5'
                      : item.status === 'Partially Signed'
                      ? 'primary--text blue lighten-5'
                      : item.status === 'Draft'
                      ? 'purple--text purple lighten-5'
                      : item.status === 'Declined' || item.status === 'Rejected'
                      ? 'error--text red lighten-5'
                      : 'grey--text grey lighten-3'
                  "
                >
                  <v-icon
                    v-if="
                      item.status === 'Completed' ||
                      item.status === 'Partially Signed'
                    "
                    :color="
                      item.status === 'Partially Signed'
                        ? '#357FCA'
                        : 'green accent-4'
                    "
                    size="25"
                    class="pr-1"
                  >
                    fas fa-check-circle
                  </v-icon>
                  <v-avatar
                    v-else-if="item.status === 'In Review'"
                    color="#D6C814"
                    size="25"
                    class="mr-1"
                  >
                    <v-icon color="white" size="13"> fas fa-search </v-icon>
                  </v-avatar>
                  <v-avatar
                    v-else-if="item.status === 'Draft'"
                    color="#FF7ACD"
                    size="25"
                    class="mr-1"
                  >
                    <v-icon color="white" size="13"> create </v-icon>
                  </v-avatar>
                  <v-icon
                    v-else-if="
                      item.status === 'Declined' || item.status === 'Rejected'
                    "
                    color="red"
                    size="25"
                    class="pr-1"
                  >
                    fas fa-times-circle
                  </v-icon>
                  <span class="body-2">{{ item.status }}</span>
                </section>
              </div>
            </td>
            <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
              <div v-if="isMobileView" class="v-data-table__mobile-row__header">
                Document Author
              </div>
              <div
                :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
              >
                <section class="primary--text body-2">
                  {{ checkNullValue(item.addedBy) }}
                </section>
              </div>
            </td>
            <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
              <div v-if="isMobileView" class="v-data-table__mobile-row__header">
                Action
              </div>
              <div
                :class="
                  isMobileView
                    ? 'v-data-table__mobile-row__cell'
                    : 'd-flex justify-end'
                "
              >
                <v-menu offset-y close-on-content-click>
                  <template #activator="{ props }">
                    <v-btn
                      v-bind="props"
                      size="small"
                      color="primary"
                      variant="outlined"
                      class="action-menu-btn"
                      icon
                      @click.stop
                    >
                      <v-icon size="20">mdi-dots-vertical</v-icon>
                    </v-btn>
                  </template>
                  <v-list
                    class="action-dropdown-menu"
                    density="compact"
                    min-width="180"
                  >
                    <v-list-item
                      class="action-menu-item"
                      @click="onClickPrint(item)"
                    >
                      <template #prepend>
                        <v-icon class="action-icon" color="primary">
                          mdi-printer
                        </v-icon>
                      </template>
                      <v-list-item-title class="action-text">
                        Print
                      </v-list-item-title>
                    </v-list-item>

                    <v-list-item
                      v-if="item.documentLink && item.status === 'Completed'"
                      class="action-menu-item"
                      @click="downloadPDF(item)"
                    >
                      <template #prepend>
                        <v-icon class="action-icon" color="success">
                          mdi-file-pdf-box
                        </v-icon>
                      </template>
                      <v-list-item-title class="action-text">
                        Download PDF
                      </v-list-item-title>
                    </v-list-item>

                    <v-list-item
                      v-if="item.status === 'Completed'"
                      class="action-menu-item"
                      @click="sendEmailToEmployee(item)"
                    >
                      <template #prepend>
                        <v-icon class="action-icon" color="info">
                          mdi-email-outline
                        </v-icon>
                      </template>
                      <v-list-item-title class="action-text">
                        Send Email
                      </v-list-item-title>
                    </v-list-item>

                    <v-divider
                      v-if="item.status !== 'Completed' && formAccess.delete"
                    />

                    <v-list-item
                      v-if="item.status !== 'Completed' && formAccess.delete"
                      class="action-menu-item action-menu-item--danger"
                      @click="
                        openDeleteConfirmationModal(item.generatedDocumentId)
                      "
                    >
                      <template #prepend>
                        <v-icon class="action-icon" color="error">
                          mdi-delete
                        </v-icon>
                      </template>
                      <v-list-item-title
                        class="action-text action-text--danger"
                      >
                        Delete
                      </v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </td>
          </tr>
        </template>
      </v-data-table>
    </div>
    <AppWarningModel
      v-if="openDeleteConfirmation"
      :open-delete-confirmation="openDeleteConfirmation"
      image-name="common/delete-bin"
      close-button-text="Cancel"
      accept-button-text="Proceed"
      confirmation-content="Are you sure to delete the selected record?"
      @close-warning-modal="closeDeleteConfirmationModal()"
      @accept-modal="deleteEmpDocument()"
    />
    <AppLoading v-if="loadingScreen" />
    <DocGeneratorEmailNotification
      v-if="openSendEmailModal"
      :open-email-modal="openSendEmailModal"
      :notify-document-details="notifyDocumentDetails"
      @close-notify-modal="openSendEmailModal = false"
    />
  </div>
</template>

<script>
import { checkNullValue, getErrorCodes, handleNetworkErrors } from "@/helper";
import {
  DELETE_EMPLOYEE_GENERATED_DOCUMENT,
  RESEND_EMAIL_TO_SIGNATORIES,
} from "@/graphql/compliance-management/docuSignQueries";
const DocGeneratorEmailNotification = () => import("./EmailNotification");

export default {
  name: "ListDocumentGenerator",

  components: {
    DocGeneratorEmailNotification,
  },

  props: {
    tableItems: {
      type: Array,
      required: true,
    },
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
  },

  data: () => ({
    // pagination
    pageCount: 1,
    page: 1,
    itemsPerPage: 50,
    pageNumber: 50,
    openPrintModal: false,
    tableHeaders: [
      {
        title: "Employee Name",
        key: "employeeName",
        sortable: true,
      },
      {
        title: "Candidate Name",
        key: "candidateName",
        sortable: true,
      },
      {
        title: "Document Name",
        key: "documentName",
        sortable: true,
      },
      {
        title: "Signatories",
        key: "authorizedSignatories",
        sortable: false,
      },
      {
        title: "Status",
        key: "status",
        sortable: true,
      },
      {
        title: "Document Author",
        key: "documentAuthor",
        sortable: true,
      },
      {
        title: "Action",
        key: "action",
        align: "end",
        sortable: false,
      },
    ],
    selectedRecords: [],
    selectedRecord: {},
    openDeleteConfirmation: false,
    errorContent: "",
    deleteEmpDocId: null,
    loadingScreen: false,
    openSendEmailModal: false,
    notifyDocumentDetails: "",
  }),

  computed: {
    // search in shares list
    searchValue() {
      return this.$store.state.empSearchValue;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },

  watch: {
    // when page number is changed
    pageNumber(val) {
      if (val === "All") {
        this.pageCount = 1;
        this.itemsPerPage = this.tableItems.length;
        this.page = 1;
      } else {
        let pageCount = this.tableItems.length / this.pageNumber;
        pageCount = pageCount <= 1 ? 1 : pageCount;
        this.pageCount = Math.round(pageCount);
        this.itemsPerPage = this.pageNumber;
        this.page = 1;
      }
    },
  },

  methods: {
    checkNullValue,
    // pageNumber is emitted from its child component to update the count in parent
    fnChangePaginationCount(val) {
      this.pageNumber = val;
    },

    // Method to return first letter in capital of each word (replaces deprecated filter)
    letterAvatar(value, isSingleLetter) {
      if (!value) return "";
      var firstChar = value ? value.charAt(0).toUpperCase() : "";
      var lastChar = value
        ? value.split(" ").pop().charAt(0).toUpperCase()
        : "";
      //condition checked for single letter avatar
      if (isSingleLetter) {
        return firstChar;
      } else {
        return firstChar + lastChar;
      }
    },

    // select the record to view
    selectDocTemplateRecord(selectedRecord) {
      this.selectedRecord = selectedRecord;
      this.$emit("action-on-doc-generator", [selectedRecord, "view"]);
    },

    downloadPDF(selectedRecord) {
      this.$emit("action-on-doc-generator", [selectedRecord, "download"]);
    },

    // on delete employees generated document
    deleteEmpDocument() {
      let vm = this;
      vm.loadingScreen = true;
      vm.closeDeleteConfirmationModal();
      try {
        vm.$apollo
          .mutate({
            mutation: DELETE_EMPLOYEE_GENERATED_DOCUMENT,
            variables: {
              generatedDocumentId: vm.deleteEmpDocId,
            },
            client: "apolloClientP",
          })
          .then(() => {
            vm.loadingScreen = false;
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Document deleted successfully.",
            };
            vm.clearFilter(); // clear search value
            vm.showAlert(snackbarData);
            vm.deleteEmpDocId = null;
            vm.$emit("delete-success");
          })
          .catch((deleteError) => {
            vm.handleDeleteError(deleteError);
          });
      } catch {
        vm.handleDeleteError();
      }
    },

    // resend email to signatories
    resendEmailToSignatories(selectedItem, selectedSignatory) {
      let vm = this;
      vm.loadingScreen = true;
      try {
        const { generatedDocumentId, documentName } = selectedItem;
        const {
          signatureEmployeeId,
          signatureEmployeeName,
          emailId,
          signatureKey,
        } = selectedSignatory;
        let isCandidate = signatureKey === "Candidate" ? 1 : 0;
        vm.$apollo
          .mutate({
            mutation: RESEND_EMAIL_TO_SIGNATORIES,
            variables: {
              documentId: generatedDocumentId,
              documentName: documentName,
              signatoryId: signatureEmployeeId,
              signatoryEmailAddress: emailId,
              signatoryName: signatureEmployeeName,
              isCandidate: isCandidate,
            },
            client: "apolloClientO",
          })
          .then(() => {
            vm.loadingScreen = false;
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Email resent successfully to the signatory",
            };
            vm.showAlert(snackbarData);
          })
          .catch((resendErr) => {
            vm.handleResendEmailError(resendErr);
          });
      } catch {
        vm.handleResendEmailError();
      }
    },

    // open warning alert before triggering the delete event
    openDeleteConfirmationModal(deleteId) {
      this.deleteEmpDocId = deleteId;
      this.openDeleteConfirmation = true;
    },

    // while closing the delete warning alert
    closeDeleteConfirmationModal() {
      this.openDeleteConfirmation = false;
    },

    // send notification
    sendEmailToEmployee(item) {
      const { employeeEmail } = this.$store.state.userDetails;
      if (employeeEmail) {
        this.notifyDocumentDetails = item;
        this.openSendEmailModal = true;
      } else {
        let snackbarData = {
          isOpen: true,
          type: "warning",
          message:
            "Your account is not associated with an email address. Hence you cannot share a document with others.",
        };
        this.showAlert(snackbarData);
      }
    },

    // clear search and filter data
    clearFilter() {
      this.$store.commit("UPDATE_TOPBAR_CLEAR_FLAG", true);
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },

    // while clicking print in the grid, we have to open the content in popup
    onClickPrint(selectedItem) {
      this.$emit("action-on-doc-generator", [selectedItem, "print"]);
    },

    // handle doc template delete error from backend
    handleDeleteError(err = "") {
      let snackbarData = {
        isOpen: true,
        type: "warning",
        message: "",
      };
      this.loadingScreen = false;
      this.deleteEmpDocId = null;
      // check if it is a graphql error
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // technical issues.
            snackbarData["message"] =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_DB0103": // no delete access
            snackbarData["message"] =
              "Sorry, you don't have access to delete the employee's generated document. Please contact HR administrator.";
            break;
          case "_EC0006": // Record(s) are already deleted in the same or some other user session.
            snackbarData["message"] =
              "Unable to delete the record as it was deleted already in the same or some other user session.";
            break;
          case "_UH0001": //unhandled error
          case "_DB0001": // Error while retrieving the employee access rights
          case "_DB0104": // While check access rights form not found
          case "_DB0002": // Error while checking the employee access rights
          case "CDG0105": // Error while processing the request to delete the generated document.
          case "CDG0006": // Error while deleting the generated document.
          case "_EC0007": // Invalid input field(s).
          default:
            snackbarData["message"] =
              "Something went wrong while deleting the employee's generated document. If you continue to see this issue, please contact the platform administrator.";
            break;
        }
      } else if (err && err.networkError) {
        snackbarData["message"] = handleNetworkErrors(err);
      } else {
        snackbarData["message"] =
          "Something went wrong while deleting the employee's generated document. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },

    // handle resend email error
    handleResendEmailError(err = "") {
      let snackbarData = {
        isOpen: true,
        type: "warning",
        message: "",
      };
      this.loadingScreen = false;
      // check if it is a graphql error
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // technical issues.
            snackbarData["message"] =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_EC0007": // Invalid input field(s).
          case "_UH0001": // unhandled error
          case "PBP0105": // Error in sending email
          case "CDG0123": // Error while processing the request to resend the signature link to the signatory.
          case "CDG0016": // Error while resending the signature link to the signatory.
          default:
            snackbarData["message"] =
              "Something went wrong while resending the document to signatory. If you continue to see this issue, please contact the platform administrator.";
            break;
        }
      } else if (err && err.networkError) {
        snackbarData["message"] = handleNetworkErrors(err);
      } else {
        snackbarData["message"] =
          "Something went wrong while resending the document to signatory. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },
    checkIfSignedByAll(data) {
      const allNonCandidatesSigned = data
        .filter((item) => item.signatureKey.toLowerCase() !== "candidate")
        .every((item) => item.status.toLowerCase() === "signed");
      return allNonCandidatesSigned;
    },

    // show the message in snack bar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
/* Ensure all table rows have white background */
:deep(.v-data-table tbody tr) {
  background-color: white !important;
}

:deep(.v-data-table__mobile-table-row) {
  background-color: white !important;
}
</style>
